package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.AdquirenteDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroAdquirenteJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.AdquirenteService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.adquirente.ExemploRespostaAdquirente;
import com.pacto.cadastrosAuxiliares.swagger.respostas.adquirente.ExemploRespostaListAdquirentePaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/adquirente")
public class AdquirenteController {

    private final AdquirenteService service;

    public AdquirenteController(AdquirenteService service) {
        this.service = service;
    }

    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     PaginadorDTO paginadorDTO) {
        try {
            FiltroAdquirenteJSON filtroAdquirenteJSON = new FiltroAdquirenteJSON(filtros);
            return ResponseEntityFactory.ok(service.findAll(filtroAdquirenteJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> adquirente(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.adquirente(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluir(@RequestBody AdquirenteDTO dto) {
        try {
            return ResponseEntityFactory.ok(service.saveOrUpdate(dto));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id ) {
        try {
            service.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

}
