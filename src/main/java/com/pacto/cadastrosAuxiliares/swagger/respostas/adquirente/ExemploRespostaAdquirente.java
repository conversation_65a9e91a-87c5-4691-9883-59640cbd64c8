package com.pacto.cadastrosAuxiliares.swagger.respostas.adquirente;

import com.pacto.cadastrosAuxiliares.dto.basico.AdquirenteDTO;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(name = "Exemplo Resposta - Adquirente", description = "Exemplo da resposta contendo as informações de um adquirente")
public class ExemploRespostaAdquirente {

    @Schema(description = "Conteú<PERSON> da resposta contendo as informações solicitadas na requisição")
    private AdquirenteDTO content;

    public AdquirenteDTO getContent() {
        return content;
    }

    public void setContent(AdquirenteDTO content) {
        this.content = content;
    }
}
